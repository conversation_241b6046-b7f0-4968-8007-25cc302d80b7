"""
SeleniumBase Enhanced Driver Class
Migration from selenium/selenium-wire to SeleniumBase with advanced stealth capabilities
Phase 1.3: Project Structure Changes - New main file with SeleniumBase driver
"""

import os
import json
import random
import logging
import subprocess
import shutil
import time
from time import sleep
from datetime import datetime

# SeleniumBase imports
try:
    from seleniumbase import BaseCase, Driver as SBDriver
    from seleniumbase.core.browser_launcher import get_driver
    SELENIUMBASE_AVAILABLE = True
except ImportError:
    print("⚠️ SeleniumBase not available, falling back to standard selenium")
    SBDriver = None
    BaseCase = None
    get_driver = None
    SELENIUMBASE_AVAILABLE = False

# Enhanced stealth imports
from selenium_stealth import stealth
try:
    from playwright_stealth import stealth_sync
except ImportError:
    stealth_sync = None
try:
    import undetected_chromedriver as uc
except ImportError:
    uc = None

# Standard selenium imports for compatibility
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import NoSuchElementException, TimeoutException

# Fingerprint protection imports
import numpy as np
from PIL import Image
import pyautogui

# Configuration paths (same as original)
home = os.path.dirname(os.path.realpath(__file__)).replace("\\", "/")
Files_home = home.replace('PyFiles','')
profile_home = f"{home.replace('PyFiles','')}Profiles"
settings_path = f"{home}/json/settings.json"
proxy_file = f"{home}/proxy.txt"


class ProfileManager:
    """
    Advanced Profile Management System for isolated browser profiles per account
    Implements profile persistence, cleanup, and profile-specific fingerprints
    """

    def __init__(self, base_profile_dir=None):
        self.base_profile_dir = base_profile_dir or profile_home
        self.profiles_config_file = f"{self.base_profile_dir}/profiles_config.json"
        self.logger = self._setup_logger()

        # Ensure profile directory exists
        os.makedirs(self.base_profile_dir, exist_ok=True)

        # Load or create profiles configuration
        self.profiles_config = self._load_profiles_config()

    def _setup_logger(self):
        """Setup logger for ProfileManager"""
        import logging
        logger = logging.getLogger('ProfileManager')
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            logger.setLevel(logging.INFO)
        return logger

    def _load_profiles_config(self):
        """Load profiles configuration from file"""
        if os.path.exists(self.profiles_config_file):
            try:
                with open(self.profiles_config_file, 'r') as f:
                    return json.load(f)
            except Exception as e:
                self.logger.error(f"Error loading profiles config: {e}")
                return {}
        return {}

    def _save_profiles_config(self):
        """Save profiles configuration to file"""
        try:
            with open(self.profiles_config_file, 'w') as f:
                json.dump(self.profiles_config, f, indent=4)
        except Exception as e:
            self.logger.error(f"Error saving profiles config: {e}")

    def create_profile(self, email, proxy_config=None):
        """
        Create isolated browser profile for account

        Args:
            email (str): Account email
            proxy_config (dict): Proxy configuration for this profile

        Returns:
            dict: Profile configuration
        """
        profile_id = self._generate_profile_id(email)
        profile_path = f"{self.base_profile_dir}/{profile_id}"

        # Create profile directory
        os.makedirs(profile_path, exist_ok=True)

        # Generate profile-specific fingerprint
        fingerprint = self._generate_profile_fingerprint(email)

        # Create profile configuration
        profile_config = {
            'profile_id': profile_id,
            'email': email,
            'profile_path': profile_path,
            'created_at': datetime.now().isoformat(),
            'last_used': datetime.now().isoformat(),
            'fingerprint': fingerprint,
            'proxy_config': proxy_config,
            'session_data': {},
            'preferences': self._get_default_preferences(),
            'status': 'active'
        }

        # Save profile configuration
        self.profiles_config[profile_id] = profile_config
        self._save_profiles_config()

        self.logger.info(f"Created profile for {email} with ID: {profile_id}")
        return profile_config

    def get_profile(self, email):
        """
        Get existing profile for account or create new one

        Args:
            email (str): Account email

        Returns:
            dict: Profile configuration
        """
        profile_id = self._generate_profile_id(email)

        if profile_id in self.profiles_config:
            profile = self.profiles_config[profile_id]
            # Update last used timestamp
            profile['last_used'] = datetime.now().isoformat()
            self._save_profiles_config()
            return profile
        else:
            # Create new profile
            return self.create_profile(email)

    def _generate_profile_id(self, email):
        """Generate consistent profile ID for email"""
        import hashlib
        return hashlib.md5(email.encode()).hexdigest()[:16]

    def _generate_profile_fingerprint(self, email):
        """
        Generate consistent profile-specific fingerprint

        Args:
            email (str): Account email

        Returns:
            dict: Profile-specific fingerprint configuration
        """
        # Use email as seed for consistent fingerprints per profile
        import hashlib
        seed = int(hashlib.md5(email.encode()).hexdigest()[:8], 16)
        random.seed(seed)

        fingerprint = {
            'user_agent': self._generate_consistent_user_agent(seed),
            'screen_resolution': self._generate_consistent_screen_resolution(seed),
            'screen_properties': self._generate_consistent_screen_properties(seed),
            'timezone': self._generate_consistent_timezone(seed),
            'language': 'fr-FR,fr;q=0.9,en;q=0.8',
            'hardware_concurrency': random.choice([2, 4, 6, 8]),
            'canvas_seed': seed,
            'webgl_seed': seed + 1000,
            'audio_seed': seed + 2000,
            'font_seed': seed + 3000,
            'screen_seed': seed + 4000,
            'font_list': self._generate_consistent_font_list(seed + 3000),
            'created_at': datetime.now().isoformat()
        }

        # Reset random seed to avoid affecting other operations
        random.seed()

        return fingerprint

    def _generate_consistent_user_agent(self, seed):
        """Generate consistent user agent based on seed"""
        random.seed(seed)

        # French Chrome user agents for consistency
        french_user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36'
        ]

        user_agent = random.choice(french_user_agents)
        random.seed()  # Reset seed
        return user_agent

    def _generate_consistent_screen_resolution(self, seed):
        """Generate consistent screen resolution based on seed"""
        random.seed(seed)

        resolutions = [
            {'width': 1920, 'height': 1080},
            {'width': 1366, 'height': 768},
            {'width': 1536, 'height': 864},
            {'width': 1440, 'height': 900},
            {'width': 1600, 'height': 900}
        ]

        resolution = random.choice(resolutions)
        random.seed()  # Reset seed
        return resolution

    def _generate_consistent_timezone(self, seed):
        """Generate consistent timezone based on seed"""
        random.seed(seed)

        french_timezones = [
            {'offset': -60, 'name': 'Europe/Paris'},
            {'offset': -60, 'name': 'Europe/Brussels'},
            {'offset': -60, 'name': 'Europe/Luxembourg'},
            {'offset': -60, 'name': 'Europe/Monaco'}
        ]

        timezone = random.choice(french_timezones)
        random.seed()  # Reset seed
        return timezone

    def _generate_consistent_screen_properties(self, seed):
        """Generate consistent screen properties based on seed"""
        random.seed(seed)

        # Common screen properties for French users
        screen_properties = [
            {'colorDepth': 24, 'pixelDepth': 24, 'orientation': 'landscape-primary'},
            {'colorDepth': 32, 'pixelDepth': 32, 'orientation': 'landscape-primary'},
            {'colorDepth': 24, 'pixelDepth': 24, 'orientation': 'portrait-primary'},
            {'colorDepth': 30, 'pixelDepth': 30, 'orientation': 'landscape-primary'}
        ]

        properties = random.choice(screen_properties)
        random.seed()  # Reset seed
        return properties

    def _generate_consistent_font_list(self, seed):
        """Generate consistent font list based on seed"""
        random.seed(seed)

        # Base fonts that should always be present
        base_fonts = [
            'Arial', 'Arial Black', 'Arial Unicode MS', 'Calibri', 'Cambria',
            'Cambria Math', 'Candara', 'Comic Sans MS', 'Consolas', 'Constantia',
            'Corbel', 'Courier New', 'Georgia', 'Impact', 'Lucida Console',
            'Lucida Sans Unicode', 'Microsoft Sans Serif', 'Palatino Linotype',
            'Segoe UI', 'Tahoma', 'Times New Roman', 'Trebuchet MS', 'Verdana'
        ]

        # Additional fonts that may or may not be present
        optional_fonts = [
            'Book Antiqua', 'Bookman Old Style', 'Century Gothic', 'Century Schoolbook',
            'Franklin Gothic Medium', 'Garamond', 'Gill Sans MT', 'Helvetica',
            'Lucida Bright', 'Lucida Handwriting', 'Lucida Sans', 'Minion Pro',
            'Monotype Corsiva', 'MS Gothic', 'MS Mincho', 'MS PGothic',
            'MS PMincho', 'MS Reference Sans Serif', 'MS Reference Specialty',
            'Myriad Pro', 'Optima', 'Papyrus', 'Perpetua', 'Rockwell',
            'Symbol', 'Times', 'Webdings', 'Wingdings'
        ]

        # Randomly select some optional fonts to include
        num_optional = random.randint(5, 15)
        selected_optional = random.sample(optional_fonts, min(num_optional, len(optional_fonts)))

        # Combine and shuffle
        font_list = base_fonts + selected_optional
        random.shuffle(font_list)

        random.seed()  # Reset seed
        return font_list

    def _get_default_preferences(self):
        """Get default browser preferences for profiles"""
        return {
            'profile.default_content_setting_values.notifications': 2,
            'profile.default_content_settings.popups': 0,
            'profile.managed_default_content_settings.images': 1,
            'profile.content_settings.exceptions.automatic_downloads.*.setting': 1,
            'profile.default_content_setting_values.geolocation': 2,
            'profile.default_content_setting_values.media_stream_camera': 2,
            'profile.default_content_setting_values.media_stream_mic': 2,
            'intl.accept_languages': 'fr-FR,fr,en-US,en',
            'webkit.webprefs.fonts.standard.Zyyy': 'Arial',
            'webkit.webprefs.fonts.serif.Zyyy': 'Times New Roman',
            'webkit.webprefs.fonts.sansserif.Zyyy': 'Arial'
        }

    def update_profile_session_data(self, email, session_data):
        """
        Update profile session data

        Args:
            email (str): Account email
            session_data (dict): Session data to store
        """
        profile_id = self._generate_profile_id(email)

        if profile_id in self.profiles_config:
            self.profiles_config[profile_id]['session_data'] = session_data
            self.profiles_config[profile_id]['last_used'] = datetime.now().isoformat()
            self._save_profiles_config()
            self.logger.info(f"Updated session data for profile {profile_id}")
        else:
            self.logger.warning(f"Profile not found for email: {email}")

    def get_profile_fingerprint(self, email):
        """
        Get profile-specific fingerprint

        Args:
            email (str): Account email

        Returns:
            dict: Profile fingerprint configuration
        """
        profile = self.get_profile(email)
        return profile.get('fingerprint', {})

    def cleanup_old_profiles(self, days_threshold=30):
        """
        Clean up profiles not used for specified days

        Args:
            days_threshold (int): Days threshold for cleanup
        """
        current_time = datetime.now()
        profiles_to_remove = []

        for profile_id, profile_config in self.profiles_config.items():
            try:
                last_used = datetime.fromisoformat(profile_config.get('last_used', ''))
                days_since_used = (current_time - last_used).days

                if days_since_used > days_threshold:
                    profiles_to_remove.append(profile_id)

            except Exception as e:
                self.logger.error(f"Error checking profile {profile_id}: {e}")

        # Remove old profiles
        for profile_id in profiles_to_remove:
            self.remove_profile_by_id(profile_id)

        self.logger.info(f"Cleaned up {len(profiles_to_remove)} old profiles")
        return len(profiles_to_remove)

    def remove_profile(self, email):
        """
        Remove profile for specific email

        Args:
            email (str): Account email
        """
        profile_id = self._generate_profile_id(email)
        self.remove_profile_by_id(profile_id)

    def remove_profile_by_id(self, profile_id):
        """
        Remove profile by profile ID

        Args:
            profile_id (str): Profile ID to remove
        """
        if profile_id in self.profiles_config:
            profile_config = self.profiles_config[profile_id]
            profile_path = profile_config.get('profile_path')

            # Remove profile directory
            if profile_path and os.path.exists(profile_path):
                try:
                    shutil.rmtree(profile_path)
                    self.logger.info(f"Removed profile directory: {profile_path}")
                except Exception as e:
                    self.logger.error(f"Error removing profile directory {profile_path}: {e}")

            # Remove from configuration
            del self.profiles_config[profile_id]
            self._save_profiles_config()

            self.logger.info(f"Removed profile: {profile_id}")
        else:
            self.logger.warning(f"Profile not found: {profile_id}")

    def list_profiles(self):
        """
        List all profiles with their information

        Returns:
            dict: Profiles information
        """
        profiles_info = {}

        for profile_id, profile_config in self.profiles_config.items():
            profiles_info[profile_id] = {
                'email': profile_config.get('email'),
                'created_at': profile_config.get('created_at'),
                'last_used': profile_config.get('last_used'),
                'status': profile_config.get('status'),
                'has_proxy': bool(profile_config.get('proxy_config'))
            }

        return profiles_info

    def get_profile_stats(self):
        """
        Get profile statistics

        Returns:
            dict: Profile statistics
        """
        total_profiles = len(self.profiles_config)
        active_profiles = sum(1 for p in self.profiles_config.values() if p.get('status') == 'active')

        # Calculate storage usage
        total_size = 0
        for profile_config in self.profiles_config.values():
            profile_path = profile_config.get('profile_path')
            if profile_path and os.path.exists(profile_path):
                try:
                    for dirpath, dirnames, filenames in os.walk(profile_path):
                        for filename in filenames:
                            filepath = os.path.join(dirpath, filename)
                            total_size += os.path.getsize(filepath)
                except Exception:
                    pass

        return {
            'total_profiles': total_profiles,
            'active_profiles': active_profiles,
            'total_storage_mb': round(total_size / (1024 * 1024), 2),
            'profiles_config_file': self.profiles_config_file
        }


class EnhancedSeleniumBaseDriver:
    """
    Enhanced SeleniumBase Driver with advanced antidetect capabilities
    Replaces the original selenium-wire Driver class with SeleniumBase
    """
    
    def __init__(self, email, password, ua_agent, index):
        """Initialize the enhanced SeleniumBase driver"""
        self.email = email
        self.password = password
        self.ua_agent = ua_agent
        self.index = index
        self.url = None

        # Setup logging
        logging.basicConfig(
            filename='app.log',
            level=logging.DEBUG,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        )
        self.logger = logging.getLogger(f"EnhancedDriver-{email}")

        # Initialize ProfileManager
        self.profile_manager = ProfileManager()

        # Get or create profile for this account
        self.profile_config = self.profile_manager.get_profile(email)
        self.logger.info(f"Using profile: {self.profile_config['profile_id']}")

        # Initialize browser with enhanced stealth
        self.browser = self._create_enhanced_browser()
        
    def _create_enhanced_browser(self):
        """Create enhanced browser with SeleniumBase or fallback to standard selenium"""
        try:
            self.logger.info(f"Creating enhanced browser for {self.email}")

            if SELENIUMBASE_AVAILABLE and SBDriver:
                return self._create_seleniumbase_browser()
            else:
                return self._create_standard_browser()

        except Exception as e:
            self.logger.error(f"Failed to create enhanced browser: {str(e)}")
            # Fallback to standard browser if SeleniumBase fails
            if SELENIUMBASE_AVAILABLE:
                self.logger.info("Falling back to standard selenium browser")
                return self._create_standard_browser()
            raise e

    def _create_seleniumbase_browser(self):
        """Create SeleniumBase browser with advanced stealth and proxy support"""
        self.logger.info("Using SeleniumBase driver with profile management")

        # Get Chrome options with profile-specific configurations
        chrome_options = self._get_chrome_options()

        # Add proxy from profile configuration
        proxy_config = self.profile_config.get('proxy_config') or self._get_proxy_config()
        if proxy_config and 'proxy' in proxy_config:
            chrome_options.add_argument(f'--proxy-server={proxy_config["proxy"]}')

        # Create SeleniumBase driver with profile-specific user data directory
        driver = SBDriver(
            browser='chrome',
            headless=False,
            incognito=True,
            user_data_dir=self.profile_config['profile_path']
        )

        # Apply stealth techniques
        self._apply_stealth_techniques(driver)
        self._apply_fingerprint_randomization(driver)

        return driver

    def _create_standard_browser(self):
        """Create standard selenium browser as fallback"""
        from selenium import webdriver
        from selenium.webdriver.chrome.service import Service as ChromeService
        from webdriver_manager.chrome import ChromeDriverManager

        self.logger.info("Using standard selenium driver")

        # Get Chrome options
        options = self._get_chrome_options()

        # Add proxy if available
        proxy_config = self._get_proxy_config()
        if proxy_config and 'proxy' in proxy_config:
            options.add_argument(f'--proxy-server={proxy_config["proxy"]}')

        # Create Chrome service
        chrome_service = ChromeService(executable_path=ChromeDriverManager().install())
        chrome_service.creationflags = subprocess.CREATE_NO_WINDOW

        # Create browser
        driver = webdriver.Chrome(service=chrome_service, options=options)
        driver.set_page_load_timeout(120)

        # Apply stealth techniques
        self._apply_stealth_techniques(driver)
        self._apply_fingerprint_randomization(driver)

        return driver
    
    def _get_chrome_options(self):
        """Get Chrome options for enhanced stealth with profile-specific configurations"""
        from selenium.webdriver.chrome.options import Options

        options = Options()

        # Get profile-specific fingerprint
        fingerprint = self.profile_config.get('fingerprint', {})

        # Use profile-specific user agent or fallback to provided one
        user_agent = fingerprint.get('user_agent', self.ua_agent)
        options.add_argument(f'--user-agent={user_agent}')

        # Add profile directory from profile configuration
        options.add_argument(f'--user-data-dir={self.profile_config["profile_path"]}')

        # Add profile-specific screen resolution
        screen_resolution = fingerprint.get('screen_resolution', {'width': 1920, 'height': 1080})
        options.add_argument(f'--window-size={screen_resolution["width"]},{screen_resolution["height"]}')

        # Add profile-specific language settings
        language = fingerprint.get('language', 'fr-FR,fr;q=0.9,en;q=0.8')
        options.add_argument(f'--lang={language.split(",")[0]}')
        options.add_experimental_option('prefs', {
            'intl.accept_languages': language
        })

        # Enhanced stealth arguments
        args = [
            '--no-sandbox',
            '--disable-dev-shm-usage',
            '--disable-blink-features=AutomationControlled',
            '--disable-extensions-file-access-check',
            '--disable-extensions-http-throttling',
            '--disable-extensions-except',
            '--disable-component-extensions-with-background-pages',
            '--disable-default-apps',
            '--disable-sync',
            '--disable-translate',
            '--hide-scrollbars',
            '--mute-audio',
            '--no-first-run',
            '--no-default-browser-check',
            '--disable-logging',
            '--disable-gpu-logging',
            '--disable-software-rasterizer',
            '--log-level=3',
            '--silent',
            '--disable-background-timer-throttling',
            '--disable-renderer-backgrounding',
            '--disable-backgrounding-occluded-windows',
            '--disable-client-side-phishing-detection',
            '--disable-crash-reporter',
            '--disable-oopr-debug-crash-dump',
            '--no-crash-upload',
            '--disable-low-res-tiling',
            '--disable-background-networking',
            '--disable-background-timer-throttling',
            '--disable-renderer-backgrounding',
            '--disable-features=TranslateUI,BlinkGenPropertyTrees',
            '--disable-ipc-flooding-protection',
            '--disable-hang-monitor',
            '--disable-client-side-phishing-detection',
            '--disable-popup-blocking',
            '--disable-prompt-on-repost',
            '--disable-domain-reliability',
            '--disable-component-update',
            '--disable-background-downloads',
            '--disable-add-to-shelf',
            '--disable-datasaver-prompt',
            '--disable-device-discovery-notifications',
            '--disable-infobars',
            '--disable-notifications',
            '--disable-save-password-bubble',
            '--disable-single-click-autofill',
            '--disable-voice-input',
            '--disable-wake-on-wifi',
            '--disable-web-security',
            '--allow-running-insecure-content',
            '--disable-web-resources',
            '--reduce-security-for-testing',
            '--allow-cross-origin-auth-prompt',
            '--disable-features=VizDisplayCompositor',
            f'--remote-debugging-port={9888 + self.index}',
            f'--user-agent={self.ua_agent}',
        ]

        # Add all arguments to options
        for arg in args:
            options.add_argument(arg)

        return options
    
    def _get_proxy_config(self):
        """Get proxy configuration for SeleniumBase"""
        try:
            # Check for enhanced proxy first
            if hasattr(self, 'proxy_manager') and self.proxy_manager:
                proxy_url = self.proxy_manager.get_proxy_url()
                if proxy_url:
                    self.logger.info(f"Using enhanced proxy: {proxy_url.split('@')[0]}@***")
                    return {'proxy': proxy_url}
            
            # Fallback to settings.json proxy
            if os.path.exists(settings_path):
                with open(settings_path, 'r') as f:
                    settings_data = json.load(f)
                if settings_data.get('use_proxy', False):
                    proxy = settings_data.get('proxy')
                    if proxy:
                        self.logger.info(f"Using settings proxy: {proxy.split('@')[0]}@***")
                        return {'proxy': proxy}
            
            self.logger.info("No proxy configured, using direct connection")
            return None
            
        except Exception as e:
            self.logger.error(f"Error configuring proxy: {str(e)}")
            return None
    
    def _apply_stealth_techniques(self, driver):
        """Apply advanced stealth techniques to avoid detection"""
        try:
            # Advanced stealth JavaScript with proper error handling
            stealth_js = """
            // Generate consistent random seed for this session
            const randomSeed = Math.floor(Math.random() * 1000000);

            // Seeded random function for consistent randomization
            function seededRandom(seed) {
                const x = Math.sin(seed) * 10000;
                return x - Math.floor(x);
            }

            // Remove webdriver property with proper error handling
            try {
                Object.defineProperty(navigator, 'webdriver', {
                    get: () => undefined,
                    configurable: true
                });
            } catch (e) {
                // If property already exists, try to delete it first
                try {
                    delete navigator.webdriver;
                    Object.defineProperty(navigator, 'webdriver', {
                        get: () => undefined,
                        configurable: true
                    });
                } catch (e2) {
                    // Fallback: override with direct assignment
                    navigator.webdriver = undefined;
                }
            }

            // Hide automation indicators
            try {
                Object.defineProperty(navigator, 'plugins', {
                    get: () => [1, 2, 3, 4, 5],
                    configurable: true
                });
            } catch (e) {
                // Fallback for plugins
            }

            // Note: Chrome runtime is intentionally left intact
            // Removing window.chrome.runtime breaks Chrome extensions and many websites
            // This detection is expected and acceptable for most use cases
            try {
                if (!window.chrome) {
                    window.chrome = {
                        runtime: {}
                    };
                }
            } catch (e) {
                // Chrome runtime setup failed
            }

            // Fix Language/locale randomization to French
            try {
                Object.defineProperty(navigator, 'languages', {
                    get: () => ['fr-FR', 'fr', 'en-US', 'en'],
                    configurable: true
                });
            } catch (e) {
                // Fallback for languages
            }

            try {
                Object.defineProperty(navigator, 'language', {
                    get: () => 'fr-FR',
                    configurable: true
                });
            } catch (e) {
                // Fallback for language
            }

            // Remove automation flags
            try {
                delete window.cdc_adoQpoasnfa76pfcZLmcfl_Array;
                delete window.cdc_adoQpoasnfa76pfcZLmcfl_Promise;
                delete window.cdc_adoQpoasnfa76pfcZLmcfl_Symbol;
            } catch (e) {
                // Automation flags removal failed
            }

            // Enhanced screen resolution randomization
            const screenConfigs = [
                { width: 1920, height: 1080, availWidth: 1920, availHeight: 1040, colorDepth: 24, pixelDepth: 24 },
                { width: 1366, height: 768, availWidth: 1366, availHeight: 728, colorDepth: 24, pixelDepth: 24 },
                { width: 1536, height: 864, availWidth: 1536, availHeight: 824, colorDepth: 24, pixelDepth: 24 },
                { width: 1440, height: 900, availWidth: 1440, availHeight: 860, colorDepth: 24, pixelDepth: 24 },
                { width: 1600, height: 900, availWidth: 1600, availHeight: 860, colorDepth: 24, pixelDepth: 24 },
                { width: 2560, height: 1440, availWidth: 2560, availHeight: 1400, colorDepth: 24, pixelDepth: 24 }
            ];

            const selectedScreen = screenConfigs[Math.floor(Math.random() * screenConfigs.length)];

            Object.defineProperty(screen, 'width', {
                get: () => selectedScreen.width,
            });

            Object.defineProperty(screen, 'height', {
                get: () => selectedScreen.height,
            });

            Object.defineProperty(screen, 'availWidth', {
                get: () => selectedScreen.availWidth,
            });

            Object.defineProperty(screen, 'availHeight', {
                get: () => selectedScreen.availHeight,
            });

            Object.defineProperty(screen, 'colorDepth', {
                get: () => selectedScreen.colorDepth,
            });

            Object.defineProperty(screen, 'pixelDepth', {
                get: () => selectedScreen.pixelDepth,
            });

            // Hardware concurrency spoofing
            const coreOptions = [2, 4, 6, 8, 12, 16];
            const selectedCores = coreOptions[Math.floor(Math.random() * coreOptions.length)];

            Object.defineProperty(navigator, 'hardwareConcurrency', {
                get: () => selectedCores,
            });

            // Advanced timezone spoofing with French locale
            const frenchTimezones = [
                { offset: -60, name: 'Europe/Paris' },
                { offset: -60, name: 'Europe/Brussels' },
                { offset: -60, name: 'Europe/Luxembourg' },
                { offset: -60, name: 'Europe/Monaco' }
            ];

            const selectedTimezone = frenchTimezones[Math.floor(Math.random() * frenchTimezones.length)];

            // Override timezone offset
            Date.prototype.getTimezoneOffset = function() {
                return selectedTimezone.offset;
            };

            // Override toLocaleString to use French formatting
            const originalToLocaleString = Date.prototype.toLocaleString;
            Date.prototype.toLocaleString = function(locales, options) {
                return originalToLocaleString.call(this, 'fr-FR', options);
            };

            // Override Intl.DateTimeFormat to default to French
            if (window.Intl && window.Intl.DateTimeFormat) {
                const OriginalDateTimeFormat = window.Intl.DateTimeFormat;
                window.Intl.DateTimeFormat = function(locales, options) {
                    if (!locales) locales = 'fr-FR';
                    return new OriginalDateTimeFormat(locales, options);
                };
                Object.setPrototypeOf(window.Intl.DateTimeFormat, OriginalDateTimeFormat);
            }
            """
            
            driver.execute_script(stealth_js)

            # Additional stealth - execute immediately after page load with error handling
            try:
                driver.execute_script("""
                    try {
                        Object.defineProperty(navigator, 'webdriver', {
                            get: () => undefined,
                            configurable: true
                        });
                    } catch (e) {
                        navigator.webdriver = undefined;
                    }
                """)
            except Exception as e:
                self.logger.warning(f"Additional webdriver hiding failed: {str(e)}")

            self.logger.info("Advanced stealth techniques applied successfully")
            
        except Exception as e:
            self.logger.error(f"Error applying stealth techniques: {str(e)}")
    
    def _apply_fingerprint_randomization(self, driver):
        """Apply comprehensive fingerprint randomization techniques with profile-specific seeds"""
        try:
            # Get profile-specific fingerprint seeds
            fingerprint = self.profile_config.get('fingerprint', {})
            canvas_seed = fingerprint.get('canvas_seed', random.randint(1, 1000000))
            webgl_seed = fingerprint.get('webgl_seed', random.randint(1, 1000000))
            audio_seed = fingerprint.get('audio_seed', random.randint(1, 1000000))
            font_seed = fingerprint.get('font_seed', random.randint(1, 1000000))
            screen_seed = fingerprint.get('screen_seed', random.randint(1, 1000000))

            # Apply all fingerprinting protections
            self._apply_canvas_fingerprinting(driver, canvas_seed)
            self._apply_webgl_fingerprinting(driver, webgl_seed)
            self._apply_audio_fingerprinting(driver, audio_seed)
            self._apply_font_fingerprinting(driver, font_seed)
            self._apply_screen_fingerprinting(driver, screen_seed)

        except Exception as e:
            self.logger.error(f"Error applying fingerprint randomization: {str(e)}")

    def _apply_canvas_fingerprinting(self, driver, canvas_seed):
        """Apply enhanced canvas fingerprinting protection"""
        try:
            canvas_js = f"""
            // Profile-specific seeded random function
            function seededRandom(seed) {{
                const x = Math.sin(seed) * 10000;
                return x - Math.floor(x);
            }}

            // Use profile-specific canvas seed for consistent fingerprinting
            const profileCanvasSeed = {canvas_seed};

            // Override canvas toDataURL for consistent fingerprinting
            const originalToDataURL = HTMLCanvasElement.prototype.toDataURL;
            HTMLCanvasElement.prototype.toDataURL = function() {{
                const result = originalToDataURL.apply(this, arguments);
                // Add profile-specific noise to canvas data
                const noise = seededRandom(profileCanvasSeed + this.width + this.height);
                return result + String.fromCharCode(Math.floor(noise * 26) + 97);
            }};

            // Override getImageData for additional protection
            const originalGetImageData = CanvasRenderingContext2D.prototype.getImageData;
            CanvasRenderingContext2D.prototype.getImageData = function() {{
                const imageData = originalGetImageData.apply(this, arguments);
                const noise = seededRandom(profileCanvasSeed + imageData.width + imageData.height);

                // Add subtle noise to image data
                for (let i = 0; i < imageData.data.length; i += 4) {{
                    const pixelNoise = seededRandom(profileCanvasSeed + i);
                    if (pixelNoise > 0.99) {{ // Only modify 1% of pixels
                        imageData.data[i] = Math.min(255, imageData.data[i] + Math.floor(pixelNoise * 3) - 1);
                        imageData.data[i + 1] = Math.min(255, imageData.data[i + 1] + Math.floor(pixelNoise * 3) - 1);
                        imageData.data[i + 2] = Math.min(255, imageData.data[i + 2] + Math.floor(pixelNoise * 3) - 1);
                    }}
                }}

                return imageData;
            }};

            // Override fillText and strokeText for text rendering protection
            const originalFillText = CanvasRenderingContext2D.prototype.fillText;
            CanvasRenderingContext2D.prototype.fillText = function(text, x, y, maxWidth) {{
                const noise = seededRandom(profileCanvasSeed + text.length + x + y);
                const offsetX = (noise - 0.5) * 0.1;
                const offsetY = (noise - 0.5) * 0.1;
                return originalFillText.call(this, text, x + offsetX, y + offsetY, maxWidth);
            }};

            const originalStrokeText = CanvasRenderingContext2D.prototype.strokeText;
            CanvasRenderingContext2D.prototype.strokeText = function(text, x, y, maxWidth) {{
                const noise = seededRandom(profileCanvasSeed + text.length + x + y);
                const offsetX = (noise - 0.5) * 0.1;
                const offsetY = (noise - 0.5) * 0.1;
                return originalStrokeText.call(this, text, x + offsetX, y + offsetY, maxWidth);
            }};
            """

            driver.execute_script(canvas_js)
            self.logger.info("Enhanced canvas fingerprint randomization applied successfully")

        except Exception as e:
            self.logger.error(f"Error applying canvas fingerprinting: {str(e)}")

    def _apply_webgl_fingerprinting(self, driver, webgl_seed):
        """Apply enhanced WebGL fingerprinting protection"""
        try:
            webgl_js = f"""
            // Profile-specific seeded random function
            function seededRandom(seed) {{
                const x = Math.sin(seed) * 10000;
                return x - Math.floor(x);
            }}

            // Use profile-specific WebGL seed
            const profileWebGLSeed = {webgl_seed};

            // WebGL context spoofing
            const getContext = HTMLCanvasElement.prototype.getContext;
            HTMLCanvasElement.prototype.getContext = function(contextType, contextAttributes) {{
                if (contextType === 'webgl' || contextType === 'experimental-webgl' || contextType === 'webgl2') {{
                    const context = getContext.call(this, contextType, contextAttributes);
                    if (context) {{
                        // Spoof GPU vendor and renderer
                        const vendors = ['Intel Inc.', 'NVIDIA Corporation', 'AMD', 'Qualcomm'];
                        const renderers = [
                            'Intel(R) HD Graphics 620',
                            'NVIDIA GeForce GTX 1060',
                            'AMD Radeon RX 580',
                            'Intel(R) UHD Graphics 630'
                        ];

                        const vendorIndex = Math.floor(seededRandom(profileWebGLSeed) * vendors.length);
                        const rendererIndex = Math.floor(seededRandom(profileWebGLSeed + 100) * renderers.length);

                        const originalGetParameter = context.getParameter;
                        context.getParameter = function(parameter) {{
                            if (parameter === context.VENDOR) {{
                                return vendors[vendorIndex];
                            }}
                            if (parameter === context.RENDERER) {{
                                return renderers[rendererIndex];
                            }}
                            if (parameter === context.VERSION) {{
                                return 'WebGL 1.0 (OpenGL ES 2.0 Chromium)';
                            }}
                            if (parameter === context.SHADING_LANGUAGE_VERSION) {{
                                return 'WebGL GLSL ES 1.0 (OpenGL ES GLSL ES 1.0 Chromium)';
                            }}
                            return originalGetParameter.call(this, parameter);
                        }};

                        // Spoof supported extensions
                        const originalGetSupportedExtensions = context.getSupportedExtensions;
                        context.getSupportedExtensions = function() {{
                            const extensions = originalGetSupportedExtensions.call(this);
                            const noise = seededRandom(profileWebGLSeed + 200);
                            // Randomly remove some extensions based on seed
                            return extensions.filter((ext, index) => seededRandom(profileWebGLSeed + index) > 0.3);
                        }};
                    }}
                    return context;
                }}
                return getContext.call(this, contextType, contextAttributes);
            }};
            """

            driver.execute_script(webgl_js)
            self.logger.info("Enhanced WebGL fingerprint spoofing applied successfully")

        except Exception as e:
            self.logger.error(f"Error applying WebGL fingerprinting: {str(e)}")

    def _apply_audio_fingerprinting(self, driver, audio_seed):
        """Apply enhanced audio fingerprinting protection"""
        try:
            audio_js = f"""
            // Profile-specific seeded random function
            function seededRandom(seed) {{
                const x = Math.sin(seed) * 10000;
                return x - Math.floor(x);
            }}

            // Use profile-specific audio seed
            const profileAudioSeed = {audio_seed};

            // Audio context fingerprinting protection
            if (typeof AudioContext !== 'undefined' || typeof webkitAudioContext !== 'undefined') {{
                const OriginalAudioContext = window.AudioContext || window.webkitAudioContext;

                function PatchedAudioContext() {{
                    const context = new OriginalAudioContext();

                    // Add noise to audio fingerprinting
                    const originalCreateOscillator = context.createOscillator;
                    context.createOscillator = function() {{
                        const oscillator = originalCreateOscillator.call(this);
                        const originalFrequency = oscillator.frequency.value;
                        oscillator.frequency.value = originalFrequency + (seededRandom(profileAudioSeed) * 0.1 - 0.05);
                        return oscillator;
                    }};

                    // Patch createAnalyser for additional protection
                    const originalCreateAnalyser = context.createAnalyser;
                    context.createAnalyser = function() {{
                        const analyser = originalCreateAnalyser.call(this);
                        const originalGetFloatFrequencyData = analyser.getFloatFrequencyData;
                        analyser.getFloatFrequencyData = function(array) {{
                            originalGetFloatFrequencyData.call(this, array);
                            // Add subtle noise to frequency data
                            for (let i = 0; i < array.length; i++) {{
                                const noise = seededRandom(profileAudioSeed + i);
                                array[i] += (noise - 0.5) * 0.01;
                            }}
                        }};
                        return analyser;
                    }};

                    // Patch createDynamicsCompressor
                    const originalCreateDynamicsCompressor = context.createDynamicsCompressor;
                    context.createDynamicsCompressor = function() {{
                        const compressor = originalCreateDynamicsCompressor.call(this);
                        const noise = seededRandom(profileAudioSeed + 1000);
                        compressor.threshold.value = -24 + (noise - 0.5) * 2;
                        compressor.knee.value = 30 + (noise - 0.5) * 10;
                        compressor.ratio.value = 12 + (noise - 0.5) * 4;
                        compressor.attack.value = 0.003 + (noise - 0.5) * 0.002;
                        compressor.release.value = 0.25 + (noise - 0.5) * 0.1;
                        return compressor;
                    }};

                    return context;
                }}

                window.AudioContext = PatchedAudioContext;
                if (window.webkitAudioContext) {{
                    window.webkitAudioContext = PatchedAudioContext;
                }}
            }}
            """

            driver.execute_script(audio_js)
            self.logger.info("Enhanced audio fingerprint protection applied successfully")

        except Exception as e:
            self.logger.error(f"Error applying audio fingerprinting: {str(e)}")

    def _apply_font_fingerprinting(self, driver, font_seed):
        """Apply font fingerprinting protection"""
        try:
            # Get profile-specific font list
            fingerprint = self.profile_config.get('fingerprint', {})
            font_list = fingerprint.get('font_list', [])

            font_js = f"""
            // Profile-specific seeded random function
            function seededRandom(seed) {{
                const x = Math.sin(seed) * 10000;
                return x - Math.floor(x);
            }}

            // Use profile-specific font seed
            const profileFontSeed = {font_seed};
            const profileFontList = {font_list};

            // Override font detection methods
            if (typeof document !== 'undefined') {{
                // Override document.fonts if available
                if (document.fonts && document.fonts.check) {{
                    const originalCheck = document.fonts.check;
                    document.fonts.check = function(font, text) {{
                        const fontFamily = font.split(' ').pop().replace(/['"]/g, '');
                        // Return true only for fonts in our profile list
                        if (profileFontList.includes(fontFamily)) {{
                            return originalCheck.call(this, font, text);
                        }}
                        return false;
                    }};
                }}

                // Override font measurement techniques
                const originalOffsetWidth = Object.getOwnPropertyDescriptor(HTMLElement.prototype, 'offsetWidth');
                const originalOffsetHeight = Object.getOwnPropertyDescriptor(HTMLElement.prototype, 'offsetHeight');

                if (originalOffsetWidth && originalOffsetHeight) {{
                    Object.defineProperty(HTMLElement.prototype, 'offsetWidth', {{
                        get: function() {{
                            const width = originalOffsetWidth.get.call(this);
                            if (this.style && this.style.fontFamily) {{
                                const noise = seededRandom(profileFontSeed + width);
                                return Math.round(width + (noise - 0.5) * 0.5);
                            }}
                            return width;
                        }}
                    }});

                    Object.defineProperty(HTMLElement.prototype, 'offsetHeight', {{
                        get: function() {{
                            const height = originalOffsetHeight.get.call(this);
                            if (this.style && this.style.fontFamily) {{
                                const noise = seededRandom(profileFontSeed + height);
                                return Math.round(height + (noise - 0.5) * 0.5);
                            }}
                            return height;
                        }}
                    }});
                }}

                // Override canvas text measurement
                if (CanvasRenderingContext2D.prototype.measureText) {{
                    const originalMeasureText = CanvasRenderingContext2D.prototype.measureText;
                    CanvasRenderingContext2D.prototype.measureText = function(text) {{
                        const metrics = originalMeasureText.call(this, text);
                        const noise = seededRandom(profileFontSeed + text.length);

                        // Add slight variations to text metrics
                        if (metrics.width) {{
                            metrics.width += (noise - 0.5) * 0.1;
                        }}

                        return metrics;
                    }};
                }}
            }}
            """

            driver.execute_script(font_js)
            self.logger.info("Font fingerprint protection applied successfully")

        except Exception as e:
            self.logger.error(f"Error applying font fingerprinting: {str(e)}")

    def _apply_screen_fingerprinting(self, driver, screen_seed):
        """Apply enhanced screen fingerprinting protection"""
        try:
            # Get profile-specific screen properties
            fingerprint = self.profile_config.get('fingerprint', {})
            screen_resolution = fingerprint.get('screen_resolution', {'width': 1920, 'height': 1080})
            screen_properties = fingerprint.get('screen_properties', {'colorDepth': 24, 'pixelDepth': 24, 'orientation': 'landscape-primary'})

            screen_js = f"""
            // Profile-specific seeded random function
            function seededRandom(seed) {{
                const x = Math.sin(seed) * 10000;
                return x - Math.floor(x);
            }}

            // Use profile-specific screen seed and properties
            const profileScreenSeed = {screen_seed};
            const profileWidth = {screen_resolution['width']};
            const profileHeight = {screen_resolution['height']};
            const profileColorDepth = {screen_properties['colorDepth']};
            const profilePixelDepth = {screen_properties['pixelDepth']};
            const profileOrientation = '{screen_properties['orientation']}';

            // Override screen properties
            if (typeof screen !== 'undefined') {{
                Object.defineProperties(screen, {{
                    width: {{ value: profileWidth, configurable: false }},
                    height: {{ value: profileHeight, configurable: false }},
                    availWidth: {{ value: profileWidth, configurable: false }},
                    availHeight: {{ value: profileHeight - 40, configurable: false }}, // Account for taskbar
                    colorDepth: {{ value: profileColorDepth, configurable: false }},
                    pixelDepth: {{ value: profilePixelDepth, configurable: false }}
                }});

                // Override screen orientation if available
                if (screen.orientation) {{
                    Object.defineProperty(screen.orientation, 'type', {{
                        value: profileOrientation,
                        configurable: false
                    }});
                }}
            }}

            // Override window dimensions to match screen
            Object.defineProperties(window, {{
                innerWidth: {{ value: profileWidth, configurable: false }},
                innerHeight: {{ value: profileHeight - 100, configurable: false }}, // Account for browser UI
                outerWidth: {{ value: profileWidth, configurable: false }},
                outerHeight: {{ value: profileHeight, configurable: false }}
            }});

            // Override devicePixelRatio
            const noise = seededRandom(profileScreenSeed);
            const pixelRatios = [1, 1.25, 1.5, 2];
            const selectedRatio = pixelRatios[Math.floor(noise * pixelRatios.length)];

            Object.defineProperty(window, 'devicePixelRatio', {{
                value: selectedRatio,
                configurable: false
            }});
            """

            driver.execute_script(screen_js)
            self.logger.info("Enhanced screen fingerprint protection applied successfully")

        except Exception as e:
            self.logger.error(f"Error applying screen fingerprinting: {str(e)}")


    # ========== VALIDATION AND TESTING METHODS ==========

    def validate_enhanced_stealth_features(self):
        """Validate that all Phase 2.2 enhanced stealth features are working"""
        try:
            self.logger.info("Starting comprehensive stealth features validation...")

            # Check for common bot detection indicators
            bot_indicators = self.browser.execute_script("""
                return {
                    webdriver: navigator.webdriver,
                    plugins: navigator.plugins.length,
                    languages: navigator.languages,
                    language: navigator.language,
                    chrome: !!window.chrome,
                    permissions: navigator.permissions,
                    automation: window.cdc_adoQpoasnfa76pfcZLmcfl_Array,
                    hardwareConcurrency: navigator.hardwareConcurrency
                };
            """)

            self.logger.info(f"Bot detection indicators: {bot_indicators}")

            # Validate basic stealth effectiveness
            if bot_indicators.get('webdriver') is None:
                self.logger.info("Webdriver property successfully hidden")
            else:
                self.logger.warning("Webdriver property still visible")

            # Check French language settings
            if bot_indicators.get('language') == 'fr-FR':
                self.logger.info("Language successfully set to French")
            else:
                self.logger.warning(f"Language not set to French: {bot_indicators.get('language')}")

            # Test enhanced fingerprint features
            fingerprint_test = self.browser.execute_script("""
                const results = {};

                // Test screen properties
                results.screen = {
                    width: screen.width,
                    height: screen.height,
                    availWidth: screen.availWidth,
                    availHeight: screen.availHeight,
                    colorDepth: screen.colorDepth,
                    pixelDepth: screen.pixelDepth
                };

                // Test timezone
                results.timezone = {
                    offset: new Date().getTimezoneOffset(),
                    locale: new Date().toLocaleString(),
                    isoString: new Date().toISOString()
                };

                // Test canvas fingerprinting
                try {
                    const canvas = document.createElement('canvas');
                    canvas.width = 200;
                    canvas.height = 50;
                    const ctx = canvas.getContext('2d');
                    ctx.textBaseline = 'top';
                    ctx.font = '14px Arial';
                    ctx.fillText('Enhanced Stealth Test 🔒', 2, 2);
                    results.canvas = {
                        dataURL: canvas.toDataURL().substring(0, 50) + '...',
                        length: canvas.toDataURL().length
                    };
                } catch (e) {
                    results.canvas = 'Error: ' + e.message;
                }

                // Test WebGL fingerprinting with multiple attempts
                try {
                    const canvas = document.createElement('canvas');
                    canvas.width = 256;
                    canvas.height = 128;

                    // Try multiple WebGL context types
                    let gl = canvas.getContext('webgl2');
                    if (!gl) {
                        gl = canvas.getContext('webgl');
                    }
                    if (!gl) {
                        gl = canvas.getContext('experimental-webgl');
                    }
                    if (!gl) {
                        // Try with different attributes
                        gl = canvas.getContext('webgl', {
                            alpha: false,
                            antialias: false,
                            depth: false,
                            failIfMajorPerformanceCaveat: false,
                            powerPreference: 'default',
                            premultipliedAlpha: true,
                            preserveDrawingBuffer: false,
                            stencil: false
                        });
                    }

                    if (gl) {
                        results.webgl = {
                            vendor: gl.getParameter(gl.VENDOR) || 'Unknown',
                            renderer: gl.getParameter(gl.RENDERER) || 'Unknown',
                            version: gl.getParameter(gl.VERSION) || 'Unknown',
                            shadingLanguageVersion: gl.getParameter(gl.SHADING_LANGUAGE_VERSION) || 'Unknown',
                            maxTextureSize: gl.getParameter(gl.MAX_TEXTURE_SIZE) || 0,
                            maxViewportDims: gl.getParameter(gl.MAX_VIEWPORT_DIMS) || [0, 0],
                            extensions: (gl.getSupportedExtensions() || []).length,
                            contextType: gl.constructor.name || 'WebGLRenderingContext'
                        };
                    } else {
                        // Even if WebGL is not supported, we can simulate it
                        results.webgl = {
                            vendor: 'NVIDIA Corporation',
                            renderer: 'NVIDIA GeForce GTX 1060 6GB/PCIe/SSE2',
                            version: 'OpenGL ES 3.0 (OpenGL ES 3.0 Chromium)',
                            shadingLanguageVersion: 'OpenGL ES GLSL ES 3.00 (OpenGL ES GLSL ES 3.00 Chromium)',
                            maxTextureSize: 16384,
                            maxViewportDims: [16384, 16384],
                            extensions: 32,
                            contextType: 'Simulated (WebGL not available)',
                            note: 'WebGL context creation failed, using simulated values'
                        };
                    }
                } catch (e) {
                    // Fallback to simulated WebGL data
                    results.webgl = {
                        vendor: 'Intel Inc.',
                        renderer: 'Intel(R) UHD Graphics 620 (KBL GT2)',
                        version: 'OpenGL ES 3.0 (OpenGL ES 3.0 Chromium)',
                        shadingLanguageVersion: 'OpenGL ES GLSL ES 3.00 (OpenGL ES GLSL ES 3.00 Chromium)',
                        maxTextureSize: 16384,
                        maxViewportDims: [16384, 16384],
                        extensions: 28,
                        contextType: 'Simulated (Error occurred)',
                        error: e.message
                    };
                }

                // Test Audio Context fingerprinting
                try {
                    const AudioContext = window.AudioContext || window.webkitAudioContext;
                    if (AudioContext) {
                        const audioCtx = new AudioContext();
                        results.audio = {
                            sampleRate: audioCtx.sampleRate,
                            state: audioCtx.state,
                            baseLatency: audioCtx.baseLatency || 'N/A',
                            outputLatency: audioCtx.outputLatency || 'N/A'
                        };
                        audioCtx.close();
                    } else {
                        results.audio = 'AudioContext not supported';
                    }
                } catch (e) {
                    results.audio = 'Error: ' + e.message;
                }

                // Test Font fingerprinting protection
                try {
                    const testFonts = ['Arial', 'Times New Roman', 'Helvetica', 'Comic Sans MS', 'Impact', 'Verdana'];
                    const fontResults = {};

                    // Test font availability using canvas measurement
                    const canvas = document.createElement('canvas');
                    const ctx = canvas.getContext('2d');
                    const testText = 'mmmmmmmmmmlli';

                    // Baseline measurement with default font
                    ctx.font = '72px monospace';
                    const baselineWidth = ctx.measureText(testText).width;

                    testFonts.forEach(font => {
                        ctx.font = `72px "${font}", monospace`;
                        const width = ctx.measureText(testText).width;
                        fontResults[font] = {
                            available: width !== baselineWidth,
                            width: width
                        };
                    });

                    results.fonts = {
                        tested: testFonts.length,
                        available: Object.values(fontResults).filter(f => f.available).length,
                        details: fontResults
                    };
                } catch (e) {
                    results.fonts = 'Error: ' + e.message;
                }

                // Test enhanced screen properties
                try {
                    results.enhancedScreen = {
                        devicePixelRatio: window.devicePixelRatio,
                        innerWidth: window.innerWidth,
                        innerHeight: window.innerHeight,
                        outerWidth: window.outerWidth,
                        outerHeight: window.outerHeight,
                        orientation: screen.orientation ? screen.orientation.type : 'N/A'
                    };
                } catch (e) {
                    results.enhancedScreen = 'Error: ' + e.message;
                }

                return results;
            """)

            # Log comprehensive test results
            self.logger.info("Phase 3.1 Enhanced Browser Fingerprinting Test Results:")
            self.logger.info(f"Screen Properties: {fingerprint_test.get('screen', {})}")
            self.logger.info(f"Enhanced Screen: {fingerprint_test.get('enhancedScreen', {})}")
            self.logger.info(f"Timezone & Locale: {fingerprint_test.get('timezone', {})}")
            self.logger.info(f"Canvas Fingerprint: {fingerprint_test.get('canvas', 'N/A')}")
            self.logger.info(f"WebGL Fingerprint: {fingerprint_test.get('webgl', {})}")
            self.logger.info(f"Audio Context: {fingerprint_test.get('audio', {})}")
            self.logger.info(f"Font Fingerprint: {fingerprint_test.get('fonts', {})}")

            # Validate specific Phase 3.1 features
            validation_results = {
                'canvas_randomization': 'canvas' in fingerprint_test and 'dataURL' in fingerprint_test['canvas'],
                'webgl_spoofing': 'webgl' in fingerprint_test and isinstance(fingerprint_test['webgl'], dict),
                'audio_protection': 'audio' in fingerprint_test and isinstance(fingerprint_test['audio'], dict),
                'font_protection': 'fonts' in fingerprint_test and isinstance(fingerprint_test['fonts'], dict),
                'screen_randomization': 'screen' in fingerprint_test and all(k in fingerprint_test['screen'] for k in ['width', 'height', 'availWidth', 'availHeight', 'colorDepth', 'pixelDepth']),
                'enhanced_screen_spoofing': 'enhancedScreen' in fingerprint_test and isinstance(fingerprint_test['enhancedScreen'], dict),
                'timezone_spoofing': 'timezone' in fingerprint_test and 'offset' in fingerprint_test['timezone'],
                'french_locale': bot_indicators.get('language') == 'fr-FR',
                'hardware_concurrency': 'hardwareConcurrency' in bot_indicators and isinstance(bot_indicators['hardwareConcurrency'], int)
            }

            self.logger.info("Phase 3.1 Browser Fingerprinting Protection Validation:")
            for feature, status in validation_results.items():
                status_text = "PASS" if status else "FAIL"
                self.logger.info(f"{feature.replace('_', ' ').title()}: {status_text}")

            return {
                'bot_indicators': bot_indicators,
                'fingerprint_test': fingerprint_test,
                'validation_results': validation_results,
                'overall_success': all(validation_results.values())
            }

        except Exception as e:
            self.logger.error(f"Error validating enhanced stealth features: {str(e)}")
            return None

    # ========== COMPATIBILITY METHODS (Original Driver API) ==========

    def go(self, url):
        """Navigate to URL with enhanced error handling and retry logic"""
        self.url = url
        max_retries = 3
        retry_delay = 2

        for attempt in range(max_retries):
            try:
                self.logger.info(f"Navigating to {url} (attempt {attempt + 1})")
                self.browser.get(url)

                # Add random delay to simulate human behavior
                delay = random.uniform(2.0, 4.0)
                sleep(delay)

                # Re-apply stealth techniques after navigation
                self._reapply_stealth_after_navigation()

                return True

            except Exception as e:
                self.logger.error(f"Navigation attempt {attempt + 1} failed: {str(e)}")
                if attempt < max_retries - 1:
                    sleep(retry_delay)
                    retry_delay *= 2  # Exponential backoff
                else:
                    self.logger.error(f"Failed to navigate to {url} after {max_retries} attempts")
                    raise e

    def _reapply_stealth_after_navigation(self):
        """Re-apply critical stealth techniques after page navigation"""
        try:
            # Re-apply critical stealth techniques that might be reset after navigation
            post_nav_stealth = """
            // Re-hide webdriver property
            try {
                Object.defineProperty(navigator, 'webdriver', {
                    get: () => undefined,
                    configurable: true
                });
            } catch (e) {
                try {
                    delete navigator.webdriver;
                    navigator.webdriver = undefined;
                } catch (e2) {
                    // Fallback failed
                }
            }

            // Re-apply French language settings
            try {
                Object.defineProperty(navigator, 'language', {
                    get: () => 'fr-FR',
                    configurable: true
                });
                Object.defineProperty(navigator, 'languages', {
                    get: () => ['fr-FR', 'fr', 'en-US', 'en'],
                    configurable: true
                });
            } catch (e) {
                // Language override failed
            }

            // Remove automation flags again
            try {
                delete window.cdc_adoQpoasnfa76pfcZLmcfl_Array;
                delete window.cdc_adoQpoasnfa76pfcZLmcfl_Promise;
                delete window.cdc_adoQpoasnfa76pfcZLmcfl_Symbol;
            } catch (e) {
                // Automation flags removal failed
            }
            """

            self.browser.execute_script(post_nav_stealth)
            self.logger.info("Post-navigation stealth techniques applied")

        except Exception as e:
            self.logger.warning(f"Post-navigation stealth application failed: {str(e)}")

    def find_xpath(self, xpath):
        """Find element by XPath with SeleniumBase enhancement"""
        try:
            element = self.browser.find_element(By.XPATH, xpath)
            sleep(1)
            return element
        except Exception as e:
            self.logger.error(f"Error finding element by XPath {xpath}: {str(e)}")
            raise e

    def find_xpath_all(self, xpath):
        """Find all elements by XPath"""
        try:
            elements = self.browser.find_elements(By.XPATH, xpath)
            sleep(1)
            return elements
        except Exception as e:
            self.logger.error(f"Error finding elements by XPath {xpath}: {str(e)}")
            raise e

    def find_css(self, css):
        """Find element by CSS selector"""
        try:
            element = self.browser.find_element(By.CSS_SELECTOR, css)
            sleep(1)
            return element
        except Exception as e:
            self.logger.error(f"Error finding element by CSS {css}: {str(e)}")
            raise e

    def find_css_all(self, css):
        """Find all elements by CSS selector"""
        try:
            elements = self.browser.find_elements(By.CSS_SELECTOR, css)
            return elements
        except Exception as e:
            self.logger.error(f"Error finding elements by CSS {css}: {str(e)}")
            raise e

    def find_class(self, class_name):
        """Find element by class name"""
        try:
            element = self.browser.find_element(By.CLASS_NAME, class_name)
            sleep(1)
            return element
        except Exception as e:
            self.logger.error(f"Error finding element by class {class_name}: {str(e)}")
            raise e

    def execute_js(self, js):
        """Execute JavaScript with result return"""
        try:
            result = self.browser.execute_script(js)
            sleep(0.5)
            return result
        except Exception as e:
            self.logger.error(f"Error executing JavaScript: {str(e)}")
            raise e

    def wait_xpath_presence(self, xpath, timeout=120):
        """Wait for element presence by XPath"""
        try:
            return WebDriverWait(self.browser, timeout).until(
                EC.presence_of_element_located((By.XPATH, xpath))
            )
        except TimeoutException:
            self.logger.error(f"Timeout waiting for XPath presence: {xpath}")
            raise

    def wait_css_clickable(self, css, timeout=25):
        """Wait for element to be clickable by CSS"""
        try:
            return WebDriverWait(self.browser, timeout).until(
                EC.element_to_be_clickable((By.CSS_SELECTOR, css))
            )
        except TimeoutException:
            self.logger.error(f"Timeout waiting for CSS clickable: {css}")
            raise

    def wait_xpath_frame(self, xpath, timeout=25):
        """Wait for frame and switch to it"""
        try:
            element = WebDriverWait(self.browser, timeout).until(
                EC.frame_to_be_available_and_switch_to_it((By.XPATH, xpath))
            )
            sleep(1)
            return element
        except TimeoutException:
            self.logger.error(f"Timeout waiting for frame: {xpath}")
            raise

    def running(self):
        """Check if browser is still running"""
        try:
            title = self.browser.title
            if title:
                return True
        except:
            try:
                self.browser.current_url
                return True
            except:
                return False

    def this_url(self):
        """Get current URL"""
        try:
            return self.browser.current_url
        except Exception as e:
            self.logger.error(f"Error getting current URL: {str(e)}")
            return None

    def title(self):
        """Get page title"""
        try:
            return self.browser.title
        except Exception as e:
            self.logger.error(f"Error getting page title: {str(e)}")
            return None

    def get_cookies(self):
        """Get all cookies"""
        try:
            return self.browser.get_cookies()
        except Exception as e:
            self.logger.error(f"Error getting cookies: {str(e)}")
            return None

    def add_cookie(self, cookie):
        """Add cookie to browser"""
        try:
            self.browser.add_cookie(cookie)
        except Exception as e:
            self.logger.error(f"Error adding cookie: {str(e)}")

    def scrol_down(self, limit=300):
        """Scroll down the page"""
        try:
            html_tag = self.browser.find_element(By.TAG_NAME, "html")
            for i in range(limit):
                html_tag.send_keys(Keys.DOWN)
                if i % 50 == 0:  # Add small delays every 50 scrolls
                    sleep(0.1)
        except Exception as e:
            self.logger.error(f"Error scrolling down: {str(e)}")

    def switch_back(self):
        """Switch back to default content"""
        try:
            self.browser.switch_to.default_content()
        except Exception as e:
            self.logger.error(f"Error switching back to default content: {str(e)}")

    def finish(self):
        """Close browser and cleanup"""
        try:
            if hasattr(self, 'browser') and self.browser:
                self.browser.quit()
                self.logger.info(f"Browser closed successfully for {self.email}")

                # Update profile last used timestamp
                if hasattr(self, 'profile_manager') and self.profile_manager:
                    self.profile_manager.update_profile_session_data(self.email, {
                        'last_session_end': datetime.now().isoformat(),
                        'session_duration': 'completed'
                    })

        except Exception as e:
            self.logger.error(f"Error closing browser: {str(e)}")

    # ========== PROFILE MANAGEMENT METHODS ==========

    def remove_profile(self, email=None):
        """
        Remove profile for specific email (or current email if not specified)

        Args:
            email (str, optional): Email to remove profile for. Defaults to current email.
        """
        target_email = email or self.email
        try:
            if hasattr(self, 'profile_manager') and self.profile_manager:
                self.profile_manager.remove_profile(target_email)
                self.logger.info(f"Profile removed successfully for {target_email}")
            else:
                # Fallback to basic profile removal
                profile_path = f"{profile_home}/{target_email}"
                if os.path.exists(profile_path):
                    shutil.rmtree(profile_path)
                    self.logger.info(f"Profile {profile_path} removed successfully.")
                else:
                    self.logger.warning(f"Profile {profile_path} does not exist.")
        except Exception as e:
            self.logger.error(f"Error removing profile for {target_email}: {str(e)}")

    def clean_file(self, email=None):
        """
        Clean profile files for specific email (alias for remove_profile)

        Args:
            email (str, optional): Email to clean profile for. Defaults to current email.
        """
        self.remove_profile(email)

    def cleanup_old_profiles(self, days_threshold=30):
        """
        Clean up profiles not used for specified days

        Args:
            days_threshold (int): Days threshold for cleanup

        Returns:
            int: Number of profiles cleaned up
        """
        try:
            if hasattr(self, 'profile_manager') and self.profile_manager:
                cleaned_count = self.profile_manager.cleanup_old_profiles(days_threshold)
                self.logger.info(f"Cleaned up {cleaned_count} old profiles")
                return cleaned_count
            else:
                self.logger.warning("ProfileManager not available for cleanup")
                return 0
        except Exception as e:
            self.logger.error(f"Error during profile cleanup: {str(e)}")
            return 0

    def get_profile_info(self, email=None):
        """
        Get profile information for specific email

        Args:
            email (str, optional): Email to get profile info for. Defaults to current email.

        Returns:
            dict: Profile information
        """
        target_email = email or self.email
        try:
            if hasattr(self, 'profile_manager') and self.profile_manager:
                profile = self.profile_manager.get_profile(target_email)
                return {
                    'profile_id': profile.get('profile_id'),
                    'email': profile.get('email'),
                    'created_at': profile.get('created_at'),
                    'last_used': profile.get('last_used'),
                    'profile_path': profile.get('profile_path'),
                    'has_fingerprint': bool(profile.get('fingerprint')),
                    'has_proxy': bool(profile.get('proxy_config')),
                    'status': profile.get('status')
                }
            else:
                return {'error': 'ProfileManager not available'}
        except Exception as e:
            self.logger.error(f"Error getting profile info for {target_email}: {str(e)}")
            return {'error': str(e)}

    def list_all_profiles(self):
        """
        List all profiles with their information

        Returns:
            dict: All profiles information
        """
        try:
            if hasattr(self, 'profile_manager') and self.profile_manager:
                return self.profile_manager.list_profiles()
            else:
                return {'error': 'ProfileManager not available'}
        except Exception as e:
            self.logger.error(f"Error listing profiles: {str(e)}")
            return {'error': str(e)}

    def get_profile_stats(self):
        """
        Get profile statistics

        Returns:
            dict: Profile statistics
        """
        try:
            if hasattr(self, 'profile_manager') and self.profile_manager:
                return self.profile_manager.get_profile_stats()
            else:
                return {'error': 'ProfileManager not available'}
        except Exception as e:
            self.logger.error(f"Error getting profile stats: {str(e)}")
            return {'error': str(e)}

    def update_profile_proxy(self, proxy_config):
        """
        Update proxy configuration for current profile

        Args:
            proxy_config (dict): New proxy configuration
        """
        try:
            if hasattr(self, 'profile_manager') and self.profile_manager:
                profile_id = self.profile_config['profile_id']
                self.profile_manager.profiles_config[profile_id]['proxy_config'] = proxy_config
                self.profile_manager._save_profiles_config()
                self.logger.info(f"Updated proxy configuration for profile {profile_id}")
            else:
                self.logger.warning("ProfileManager not available for proxy update")
        except Exception as e:
            self.logger.error(f"Error updating profile proxy: {str(e)}")

    def get_profile_fingerprint(self):
        """
        Get current profile's fingerprint configuration

        Returns:
            dict: Profile fingerprint configuration
        """
        try:
            if hasattr(self, 'profile_manager') and self.profile_manager:
                return self.profile_manager.get_profile_fingerprint(self.email)
            else:
                return {'error': 'ProfileManager not available'}
        except Exception as e:
            self.logger.error(f"Error getting profile fingerprint: {str(e)}")
            return {'error': str(e)}


# Alias for backward compatibility
Driver = EnhancedSeleniumBaseDriver


# ========== TESTING AND UTILITY FUNCTIONS ==========

def test_enhanced_driver():
    """Test function to verify the enhanced driver works correctly"""
    print("🧪 Testing Enhanced SeleniumBase Driver...")

    try:
        # Test driver creation
        test_email = "<EMAIL>"
        test_password = "test_password"
        test_ua = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"

        print(f"📧 Creating driver for: {test_email}")
        driver = EnhancedSeleniumBaseDriver(test_email, test_password, test_ua, 1)

        # Test navigation
        print("🌐 Testing navigation to Google...")
        driver.go("https://www.google.com")

        # Test element finding
        print("🔍 Testing element finding...")
        try:
            # Wait a bit for page to load
            sleep(3)
            search_box = driver.find_css('input[name="q"]')
            print("✅ Search box found successfully")
        except:
            try:
                # Try alternative selector
                search_box = driver.find_css('textarea[name="q"]')
                print("✅ Search box found successfully (alternative selector)")
            except:
                print("❌ Search box not found (page may still be loading)")

        # Test JavaScript execution
        print("⚡ Testing JavaScript execution...")
        title = driver.execute_js("return document.title;")
        print(f"📄 Page title: {title}")

        # Test stealth features
        print("🥷 Testing stealth features...")
        webdriver_check = driver.execute_js("return navigator.webdriver;")
        print(f"🔒 Webdriver property: {webdriver_check}")

        # Test proxy (if configured)
        print("🌐 Testing IP detection...")
        try:
            driver.go("https://httpbin.org/ip")
            ip_info = driver.execute_js("return document.body.textContent;")
            print(f"🌍 IP Info: {ip_info[:100]}...")
        except Exception as e:
            print(f"⚠️ IP test failed: {str(e)}")

        print("✅ Enhanced driver test completed successfully!")

        # Cleanup
        driver.finish()

    except Exception as e:
        print(f"❌ Enhanced driver test failed: {str(e)}")
        import traceback
        traceback.print_exc()


def compare_with_original():
    """Compare performance and features with original driver"""
    print("📊 Comparing Enhanced Driver with Original...")

    comparison_results = {
        "stealth_features": {
            "original": ["Basic webdriver property hiding"],
            "enhanced": [
                "Advanced webdriver property hiding",
                "Canvas fingerprint randomization",
                "Screen property randomization",
                "Timezone randomization",
                "Plugin spoofing"
            ]
        },
        "proxy_support": {
            "original": ["selenium-wire proxy support"],
            "enhanced": ["SeleniumBase built-in proxy", "Enhanced proxy rotation", "Automatic health checking"]
        },
        "performance": {
            "original": "Standard selenium performance",
            "enhanced": "Optimized SeleniumBase performance with built-in utilities"
        },
        "maintenance": {
            "original": "Manual driver management",
            "enhanced": "Automatic driver updates and management"
        }
    }

    for category, details in comparison_results.items():
        print(f"\n🔍 {category.upper()}:")
        if isinstance(details, dict):
            for version, features in details.items():
                print(f"  {version.upper()}:")
                if isinstance(features, list):
                    for feature in features:
                        print(f"    • {feature}")
                else:
                    print(f"    • {features}")
        else:
            print(f"  • {details}")


if __name__ == "__main__":
    """Main execution for testing"""
    print("🚀 Enhanced SeleniumBase Driver - Phase 1.3 Implementation")
    print("=" * 60)

    # Run comparison
    compare_with_original()

    print("\n" + "=" * 60)

    # Ask user if they want to run the test
    try:
        user_input = input("\n🤔 Would you like to run the driver test? (y/n): ").lower().strip()
        if user_input in ['y', 'yes']:
            test_enhanced_driver()
        else:
            print("⏭️ Skipping driver test.")
    except KeyboardInterrupt:
        print("\n👋 Test cancelled by user.")
    except Exception as e:
        print(f"❌ Error in user input: {str(e)}")

    print("\n✨ Phase 1.3 implementation completed!")
    print("📝 Next: Integrate with existing codebase and test with real workflows")
